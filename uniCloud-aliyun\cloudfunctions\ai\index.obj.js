/**
 * AI 云对象主模块
 *
 * 提供智能任务执行和流式聊天功能：
 * - 任务意图识别和执行计划生成
 * - 多步骤任务的自动化执行
 * - 实时流式聊天响应
 * - Todo 工具集成和测试功能
 * - 执行上下文管理和数据传递
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const { IntelligentExecutionPlanner } = require('./modules/planner')
const { executeRobustPlan } = require('./modules/executor')
const { globalPerformanceMonitor } = require('./modules/performance')
const { OpenAI } = require('openai')
const { DEFAULT_SYSTEM_PROMPT, doubaoParams } = require('./modules/config.js')
const { ExecutionContextManager } = require('./modules/context')
const TodoTool = require('./modules/todo-tool')

module.exports = {
  /**
   * 测试 todo-tool.js 模块的各项功能
   * @returns {object} 测试结果摘要
   */
  async testTodoApi() {
    const startTime = Date.now()
    console.log(`[TodoAPI 测试] ========== 开始执行 TodoAPI 测试 ========== [${new Date().toISOString()}]`)

    const testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      testDetails: [],
      errors: [],
    }

    // 创建 TodoTool 实例
    console.log(`[TodoAPI 测试] 正在创建 TodoTool 实例... [${new Date().toISOString()}]`)
    const todoTool = new TodoTool()
    console.log(`[TodoAPI 测试] TodoTool 实例创建成功 [${new Date().toISOString()}]`)

    // 测试数据
    const testData = {
      testTaskTitle: `测试任务_${Date.now()}`,
      testTaskContent: '这是一个用于测试的任务内容',
      testProjectName: `测试项目_${Date.now()}`,
      testProjectColor: '#FF5722',
      createdTaskId: null,
      createdProjectId: null,
    }
    console.log(`[TodoAPI 测试] 测试数据初始化完成:`, testData)

    /**
     * 执行单个测试
     * @param {string} testName - 测试名称
     * @param {Function} testFunction - 测试函数
     * @returns {Promise<boolean>} 测试是否通过
     */
    const runTest = async (testName, testFunction) => {
      console.log(`[TodoAPI 测试] 开始执行测试：${testName} [${new Date().toISOString()}]`)
      testResults.totalTests++

      try {
        const testStartTime = Date.now()
        const result = await testFunction()
        const testDuration = Date.now() - testStartTime

        if (result.success) {
          testResults.passedTests++
          testResults.testDetails.push({
            name: testName,
            status: 'PASSED',
            message: result.message,
            data: result.data,
          })
          console.log(`[TodoAPI 测试] ✅ 测试通过：${testName} (耗时：${testDuration}ms) [${new Date().toISOString()}]`)
          console.log(`[TodoAPI 测试] 测试结果：${result.message}`)
          if (result.data) {
            console.log(`[TodoAPI 测试] 返回数据:`, result.data)
          }
          return true
        } else {
          testResults.failedTests++
          testResults.testDetails.push({
            name: testName,
            status: 'FAILED',
            message: result.message,
            error: result.error,
          })
          testResults.errors.push(`${testName}: ${result.message}`)
          console.log(`[TodoAPI 测试] ❌ 测试失败：${testName} (耗时：${testDuration}ms) [${new Date().toISOString()}]`)
          console.log(`[TodoAPI 测试] 失败原因：${result.message}`)
          if (result.error) {
            console.log(`[TodoAPI 测试] 错误详情:`, result.error)
          }
          return false
        }
      } catch (error) {
        testResults.failedTests++
        testResults.testDetails.push({
          name: testName,
          status: 'ERROR',
          message: error.message,
          error: error,
        })
        testResults.errors.push(`${testName}: ${error.message}`)
        console.log(`[TodoAPI 测试] 💥 测试异常：${testName} [${new Date().toISOString()}]`)
        console.log(`[TodoAPI 测试] 异常信息：${error.message}`)
        console.log(`[TodoAPI 测试] 异常堆栈:`, error.stack)
        return false
      }
    }

    // // 测试 1: 认证初始化
    // await runTest('认证初始化', async () => {
    //   const result = await todoTool.execute('initWithToken', {
    //     token:
    //       '73AE2E6CC13DD9673F421A1F3E02AED0E1BFB595FD663AFA63ED00682C85E0350ECBA76C0D9169C1842C895EC3C7FD43FA4BB3D094DAFA93E6FC18AA49B4F5302701265667560665A0D14835FCC55972EB9036F52182EC2D6CFEC251B6B3AD83385AA04082B6E13207380EE6E17F65D7D02746F0B1CB9D088DFB1EDE0D3D45D112B6963F72E74B8898CEFB2AD56ED90B75338A509771CA53C093C355F178EA86151002FFD8A51141ED48EB889B07BD4E',
    //   })

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '认证初始化失败',
    //       error: result,
    //     }
    //   }

    //   return {
    //     success: true,
    //     message: '认证初始化成功',
    //     data: result.data,
    //   }
    // })

    // // 测试 2: 获取基础数据
    // await runTest('获取基础数据', async () => {
    //   const result = await todoTool.execute('getBatchData')

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '获取基础数据失败',
    //       error: result,
    //     }
    //   }

    //   const { tasks, projects, tags } = result.data
    //   if (!Array.isArray(tasks) || !Array.isArray(projects) || !Array.isArray(tags)) {
    //     return {
    //       success: false,
    //       message: '基础数据格式不正确',
    //       error: result.data,
    //     }
    //   }

    //   return {
    //     success: true,
    //     message: `获取基础数据成功 - 任务：${tasks.length}, 项目：${projects.length}, 标签：${tags.length}`,
    //     data: {
    //       taskCount: tasks.length,
    //       projectCount: projects.length,
    //       tagCount: tags.length,
    //     },
    //   }
    // })

    // // 测试 3: 创建项目
    // await runTest('创建项目', async () => {
    //   const result = await todoTool.execute('createProject', {
    //     name: testData.testProjectName,
    //     color: testData.testProjectColor,
    //   })

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '创建项目失败',
    //       error: result,
    //     }
    //   }

    //   if (!result.data || !result.data.id) {
    //     return {
    //       success: false,
    //       message: '创建项目成功但未返回项目 ID',
    //       error: result,
    //     }
    //   }

    //   testData.createdProjectId = result.data.id

    //   return {
    //     success: true,
    //     message: `创建项目成功 - ID: ${result.data.id}`,
    //     data: result.data,
    //   }
    // })

    // // 测试 4: 创建任务
    // await runTest('创建任务', async () => {
    //   const result = await todoTool.execute('createTask', {
    //     title: testData.testTaskTitle,
    //     content: testData.testTaskContent,
    //     projectId: testData.createdProjectId,
    //   })

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '创建任务失败',
    //       error: result,
    //     }
    //   }

    //   if (!result.data || !result.data.id) {
    //     return {
    //       success: false,
    //       message: '创建任务成功但未返回任务 ID',
    //       error: result,
    //     }
    //   }

    //   testData.createdTaskId = result.data.id

    //   return {
    //     success: true,
    //     message: `创建任务成功 - ID: ${result.data.id}`,
    //     data: result.data,
    //   }
    // })

    // // 测试 5: 获取任务列表
    // await runTest('获取任务列表', async () => {
    //   const result = await todoTool.execute('getTasks', {
    //     projectId: testData.createdProjectId,
    //   })

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '获取任务列表失败',
    //       error: result,
    //     }
    //   }

    //   if (!Array.isArray(result.data)) {
    //     return {
    //       success: false,
    //       message: '任务列表格式不正确',
    //       error: result.data,
    //     }
    //   }

    //   const createdTask = result.data.find((task) => task.id === testData.createdTaskId)
    //   if (!createdTask) {
    //     return {
    //       success: false,
    //       message: '未在任务列表中找到刚创建的任务',
    //       error: result.data,
    //     }
    //   }

    //   return {
    //     success: true,
    //     message: `获取任务列表成功 - 共 ${result.data.length} 个任务`,
    //     data: {
    //       taskCount: result.data.length,
    //       foundCreatedTask: true,
    //     },
    //   }
    // })

    // 测试 6: 更新任务状态
    await runTest('更新任务状态', async () => {
      const result = await todoTool.execute('updateTask', {
        taskId: testData.createdTaskId,
        updateData: {
          status: 2, // 设置为已完成
        },
      })

      if (result.errCode) {
        return {
          success: false,
          message: result.errMsg || '更新任务状态失败',
          error: result,
        }
      }

      return {
        success: true,
        message: '更新任务状态成功',
        data: result.data,
      }
    })

    // 测试 7: 完成任务
    await runTest('完成任务', async () => {
      const result = await todoTool.execute('completeTask', {
        taskId: testData.createdTaskId,
      })

      if (result.errCode) {
        return {
          success: false,
          message: result.errMsg || '完成任务失败',
          error: result,
        }
      }

      return {
        success: true,
        message: '完成任务成功',
        data: result.data,
      }
    })

    // 测试 8: 取消完成任务
    await runTest('取消完成任务', async () => {
      const result = await todoTool.execute('uncompleteTask', {
        taskId: testData.createdTaskId,
      })

      if (result.errCode) {
        return {
          success: false,
          message: result.errMsg || '取消完成任务失败',
          error: result,
        }
      }

      return {
        success: true,
        message: '取消完成任务成功',
        data: result.data,
      }
    })

    // 测试 9: 获取单个任务详情
    await runTest('获取单个任务详情', async () => {
      const result = await todoTool.execute('getTask', {
        taskId: testData.createdTaskId,
      })

      if (result.errCode) {
        return {
          success: false,
          message: result.errMsg || '获取任务详情失败',
          error: result,
        }
      }

      if (!result.data || result.data.id !== testData.createdTaskId) {
        return {
          success: false,
          message: '获取的任务详情不匹配',
          error: result.data,
        }
      }

      return {
        success: true,
        message: '获取任务详情成功',
        data: result.data,
      }
    })

    // // 测试 10: 获取项目列表
    // await runTest('获取项目列表', async () => {
    //   const result = await todoTool.execute('getProjects')

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '获取项目列表失败',
    //       error: result,
    //     }
    //   }

    //   if (!Array.isArray(result.data)) {
    //     return {
    //       success: false,
    //       message: '项目列表格式不正确',
    //       error: result.data,
    //     }
    //   }

    //   const createdProject = result.data.find((project) => project.id === testData.createdProjectId)
    //   if (!createdProject) {
    //     return {
    //       success: false,
    //       message: '未在项目列表中找到刚创建的项目',
    //       error: result.data,
    //     }
    //   }

    //   return {
    //     success: true,
    //     message: `获取项目列表成功 - 共 ${result.data.length} 个项目`,
    //     data: {
    //       projectCount: result.data.length,
    //       foundCreatedProject: true,
    //     },
    //   }
    // })

    // // 测试 11: 获取单个项目详情
    // await runTest('获取单个项目详情', async () => {
    //   const result = await todoTool.execute('getProject', {
    //     projectId: testData.createdProjectId,
    //   })

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '获取项目详情失败',
    //       error: result,
    //     }
    //   }

    //   if (!result.data || result.data.id !== testData.createdProjectId) {
    //     return {
    //       success: false,
    //       message: '获取的项目详情不匹配',
    //       error: result.data,
    //     }
    //   }

    //   return {
    //     success: true,
    //     message: '获取项目详情成功',
    //     data: result.data,
    //   }
    // })

    // 清理测试数据 - 删除测试任务
    await runTest('清理测试任务', async () => {
      if (!testData.createdTaskId) {
        return {
          success: true,
          message: '无需清理任务（未创建）',
        }
      }

      const result = await todoTool.execute('deleteTask', {
        taskId: testData.createdTaskId,
      })

      if (result.errCode) {
        return {
          success: false,
          message: result.errMsg || '删除测试任务失败',
          error: result,
        }
      }

      return {
        success: true,
        message: '删除测试任务成功',
        data: result.data,
      }
    })

    // // 清理测试数据 - 删除测试项目
    // await runTest('清理测试项目', async () => {
    //   if (!testData.createdProjectId) {
    //     return {
    //       success: true,
    //       message: '无需清理项目（未创建）',
    //     }
    //   }

    //   const result = await todoTool.execute('deleteProject', {
    //     projectId: testData.createdProjectId,
    //   })

    //   if (result.errCode) {
    //     return {
    //       success: false,
    //       message: result.errMsg || '删除测试项目失败',
    //       error: result,
    //     }
    //   }

    //   return {
    //     success: true,
    //     message: '删除测试项目成功',
    //     data: result.data,
    //   }
    // })

    // 测试完成，返回结果

    return testResults
  },

  async chatStreamSSE({ channel, message, messages: history_records }) {
    // 参数验证阶段：确保必需参数存在，避免后续处理出错
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel，将前端传递的 channel 对象转换为可用的推送通道
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化豆包 AI 客户端，使用配置文件中的参数
      const openai = new OpenAI(doubaoParams)

      // 构建对话消息数组：系统提示词 + 历史记录 + 当前用户消息
      const messages = [
        {
          role: 'system',
          content: DEFAULT_SYSTEM_PROMPT,
        },
        ...history_records,
      ]
      if (message)
        messages.push({
          role: 'user',
          content: message,
        })

      // 推送开始消息，通知前端开始处理用户请求
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式 AI 响应，启用实时数据传输
      const streamResponse = await openai.chat.completions.create({
        messages, // 对话上下文
        model: 'doubao-seed-1-6-250615', // 使用的 AI 模型
        stream: true, // 启用流式响应，关键设置
        timeout: 300000, // 5 分钟超时（毫秒），防止长时间等待
      })

      // 初始化流式处理相关变量
      let fullContent = '' // 累积的完整 AI 响应内容，用于最终的意图解析
      let chunkCount = 0 // 推送的数据块计数，用于统计和调试
      let intentType = null // 识别的意图类型：task|chat
      let isChatReplyStarted = false // 是否开始推送闲聊回复的标志位
      let chatReply = '' // 提取的闲聊回复内容，仅用于 chat 类型

      // 正则表达式：匹配 AI 返回的意图类型和闲聊回复
      // task 类型只有意图类型，chat 类型有意图类型和闲聊回复
      const intentTypeRegex = /「意图类型」：(task|chat)/
      const chatReplyRegex = /「闲聊回复」：([\s\S]*)/

      // 流式处理 AI 响应数据
      for await (const chunk of streamResponse) {
        // 提取当前数据块的内容，处理可能的空值情况
        const content = chunk.choices[0]?.delta?.content || ''

        if (content) {
          fullContent += content // 累积完整内容，用于后续的正则匹配
          chunkCount++ // 增加数据块计数，用于统计和调试

          // 第一阶段：检测意图类型
          // 在累积的内容中查找意图类型标识，一旦找到就立即处理
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1] // 提取意图类型：task|chat

              // 立即推送意图类型到前端，让用户知道 AI 已经理解了请求类型
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送，避免重复发送相同信息
            }
          }

          // 第二阶段：根据意图类型处理内容推送
          if (intentType === 'chat') {
            // chat 类型：检测闲聊回复开始标识
            if (!isChatReplyStarted) {
              const replyMatch = chatReplyRegex.exec(fullContent)
              if (replyMatch) {
                isChatReplyStarted = true
                chatReply = replyMatch[1] // 提取已有的闲聊回复内容

                // 推送闲聊回复开始标识
                await sseChannel.write({
                  type: 'content_chunk',
                  content: chatReply,
                  timestamp: Date.now(),
                })
                continue // 跳过当前块的推送，避免重复发送
              }
            } else {
              // 持续推送闲聊回复内容块
              await sseChannel.write({
                type: 'content_chunk',
                content: content,
                timestamp: Date.now(),
              })
              chatReply += content // 累积闲聊回复内容
            }
          } else if (intentType === 'task') {
            // task 类型：不需要推送额外内容，直接进入任务执行阶段
          } else {
            // 尚未检测到意图类型，继续累积内容
          }
        }
      }

      // 任务执行阶段：task 类型触发智能任务执行
      // 只有当识别到任务意图时才执行任务，chat 类型直接返回对话结果
      if (intentType === 'task') {
        // 创建执行上下文管理器，用于管理执行过程中的数据和状态
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID())

        // 使用智能执行计划生成器创建执行计划
        // AI 会分析用户意图，并返回包含步骤和提取实体的计划
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        // 如果生成了有效的执行计划，则开始执行任务
        if (executionPlan.totalSteps > 0) {
          // 使用增强的执行引擎执行计划
          // 集成了错误处理、重试机制、性能监控等高级功能
          await executeRobustPlan(executionPlan, context, sseChannel, globalPerformanceMonitor)

          // 返回任务执行结果，包含详细的执行信息和性能报告
          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed', // 标识这是一个任务执行结果
              intentType: intentType, // 识别的意图类型
              executionPlan: executionPlan, // 完整的执行计划信息
              contextData: Array.from(context.contextData.keys()), // 上下文数据键列表
              executionTime: executionPlan.totalExecutionTime, // 总执行时间
              performanceReport: globalPerformanceMonitor.getPerformanceReport(), // 性能报告
              content: fullContent, // AI 完整回复内容
              totalChunks: chunkCount, // 推送的数据块总数
            },
          }
        }
      }

      // 推送结束消息，标识流式聊天处理完成
      // 这是正常流程的结束，通知前端可以停止等待更多数据
      await sseChannel.end({
        type: 'end',
        content: intentType === 'chat' ? chatReply : fullContent, // chat 类型返回闲聊回复，task 类型返回完整内容
        intentType: intentType, // 识别的意图类型
        totalChunks: chunkCount, // 总共推送的数据块数量
        timestamp: Date.now(),
      })

      // 返回流式聊天完成的结果
      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete', // 标识这是一个流式聊天完成结果
          content: intentType === 'chat' ? chatReply : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      // 错误处理：捕获并处理执行过程中的所有异常
      // 尝试通过 SSE Channel 发送错误消息给前端
      // 这样前端可以知道发生了错误，而不是一直等待
      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        // 如果连错误消息都发送失败，忽略该错误
      }

      // 返回错误结果给调用方
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },
}
