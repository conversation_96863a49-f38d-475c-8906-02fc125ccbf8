/**
 * Todo 工具方法扩展
 * 包含剩余的任务和项目管理方法
 */

const { API_CONFIG, TASK_CONFIG, PROJECT_CONFIG, ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  formatDateForApi,
  simplifyTaskData,
  simplifyProjectData,
  removeEmptyFields,
  parseHttpResponse,
} = require('./utils')

/**
 * 任务管理方法扩展
 */
const taskMethods = {
  /**
   * 删除任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 删除结果
   */
  async deleteTask(taskId) {
    console.log(`[TodoTool] [deleteTask] 开始删除任务，ID: ${taskId}`)

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TodoTool] [deleteTask] 参数校验失败：', validation)
      return validation
    }

    try {
      // 发送删除请求
      const response = await this._request('DELETE', `${API_CONFIG.TASK_URL}/${taskId}`)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [deleteTask] 删除任务失败：', parsedResponse)
        return parsedResponse
      }

      // 清除缓存
      this.cache.delete('batchData')

      console.log('[TodoTool] [deleteTask] 任务删除成功')
      return createSuccessResponse('任务删除成功', parsedResponse.data)
    } catch (error) {
      console.error('[TodoTool] [deleteTask] 删除任务异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除任务失败', error)
    }
  },

  /**
   * 完成任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 完成结果
   */
  async completeTask(taskId) {
    console.log(`[TodoTool] [completeTask] 开始完成任务，ID: ${taskId}`)

    return await this.updateTask(taskId, {
      status: TASK_CONFIG.STATUS.COMPLETED,
      completedTime: new Date().toISOString(),
    })
  },

  /**
   * 取消完成任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 取消完成结果
   */
  async uncompleteTask(taskId) {
    console.log(`[TodoTool] [uncompleteTask] 开始取消完成任务，ID: ${taskId}`)

    return await this.updateTask(taskId, {
      status: TASK_CONFIG.STATUS.ACTIVE,
      completedTime: null,
    })
  },

  /**
   * 获取单个任务详情
   * @param {string} taskId - 任务 ID
   * @returns {object} 任务详情
   */
  async getTask(taskId) {
    console.log(`[TodoTool] [getTask] 开始获取任务详情，ID: ${taskId}`)

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TodoTool] [getTask] 参数校验失败：', validation)
      return validation
    }

    try {
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [getTask] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { tasks, projects } = batchResult.data
      const task = tasks.find((t) => t.id === taskId)

      if (!task) {
        console.warn(`[TodoTool] [getTask] 未找到任务：${taskId}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, '任务不存在')
      }

      // 简化任务数据
      const simplifiedTask = simplifyTaskData(task)

      // 添加项目信息
      if (task.projectId) {
        const project = projects.find((p) => p.id === task.projectId)
        if (project) {
          simplifiedTask.projectName = project.name
          simplifiedTask.projectColor = project.color
        }
      }

      console.log('[TodoTool] [getTask] 获取任务详情成功')
      return createSuccessResponse('获取任务详情成功', simplifiedTask)
    } catch (error) {
      console.error('[TodoTool] [getTask] 获取任务详情异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务详情失败', error)
    }
  },

  /**
   * 批量操作任务
   * @param {object} options - 操作参数
   * @returns {object} 批量操作结果
   */
  async batchOperateTasks(options = {}) {
    const { taskIds, action } = options
    console.log(`[TodoTool] [batchOperateTasks] 开始批量操作任务，操作：${action}，任务 ID:`, taskIds)

    // 参数校验
    const validation = validateParams({ taskIds, action }, ['taskIds', 'action'])
    if (validation) {
      console.warn('[TodoTool] [batchOperateTasks] 参数校验失败：', validation)
      return validation
    }

    if (!Array.isArray(taskIds) || taskIds.length === 0) {
      return createErrorResponse(ERROR_CODES.PARAM_INVALID, '任务 ID 数组不能为空')
    }

    try {
      const results = []

      for (const taskId of taskIds) {
        let result
        switch (action) {
          case 'complete':
            result = await this.completeTask(taskId)
            break
          case 'uncomplete':
            result = await this.uncompleteTask(taskId)
            break
          case 'delete':
            result = await this.deleteTask(taskId)
            break
          default:
            result = createErrorResponse(ERROR_CODES.PARAM_INVALID, `不支持的操作：${action}`)
        }

        results.push({
          taskId: taskId,
          success: !result.errCode,
          result: result,
        })
      }

      const successCount = results.filter((r) => r.success).length
      const failCount = results.length - successCount

      console.log(`[TodoTool] [batchOperateTasks] 批量操作完成，成功：${successCount}，失败：${failCount}`)
      return createSuccessResponse('批量操作完成', {
        total: results.length,
        success: successCount,
        failed: failCount,
        results: results,
      })
    } catch (error) {
      console.error('[TodoTool] [batchOperateTasks] 批量操作异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '批量操作失败', error)
    }
  },
}

/**
 * 项目管理方法扩展
 */
const projectMethods = {
  /**
   * 获取项目列表
   * @param {object} options - 查询参数
   * @returns {object} 项目列表
   */
  async getProjects(options = {}) {
    const { keyword = null, includeClosed = false } = options
    console.log('[TodoTool] [getProjects] 开始获取项目列表，过滤条件：', {
      keyword,
      includeClosed,
    })

    try {
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [getProjects] 获取基础数据失败：', batchResult)
        return batchResult
      }

      let { projects } = batchResult.data
      let filteredProjects = []
      console.log(`[TodoTool] [getProjects] 原始项目数：${projects.length}`)

      for (const project of projects) {
        // 是否包含已关闭的项目
        if (!includeClosed && project.closed) continue

        // 关键词筛选
        if (keyword) {
          const searchText = `${project.name || ''}`.toLowerCase()
          if (!searchText.includes(keyword.toLowerCase())) continue
        }

        // 简化项目数据
        const simplifiedProject = simplifyProjectData(project)
        filteredProjects.push(simplifiedProject)
      }

      console.log(`[TodoTool] [getProjects] 筛选后项目数：${filteredProjects.length}`)
      return createSuccessResponse('获取项目列表成功', filteredProjects)
    } catch (error) {
      console.error('[TodoTool] [getProjects] 获取项目列表异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目列表失败', error)
    }
  },

  /**
   * 创建项目
   * @param {object} options - 项目数据
   * @returns {object} 创建结果
   */
  async createProject(options = {}) {
    // 使用安全的参数处理方式，类似 Python 版本
    const { name = null, color = null, kind = null } = options
    console.log(`[TodoTool] [createProject] 开始创建项目，名称：${name}`)

    // 参数校验
    const validation = validateParams({ name }, ['name'])
    if (validation) {
      console.warn('[TodoTool] [createProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 使用安全的参数处理方式，类似 Python 版本
      const safeProjectData = {
        name: name,
        color: color !== null ? color : '#3498db',
        kind: kind !== null ? kind : 'TASK',
        closed: false,
      }

      // 移除空值字段
      const cleanProjectData = removeEmptyFields(safeProjectData)
      console.log('[TodoTool] [createProject] 准备发送的项目数据：', cleanProjectData)

      // 发送创建请求
      const response = await this._request('POST', API_CONFIG.PROJECT_URL, cleanProjectData)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [createProject] 创建项目失败：', parsedResponse)
        return parsedResponse
      }

      // 清除缓存
      this.cache.delete('batchData')

      console.log('[TodoTool] [createProject] 项目创建成功')
      return createSuccessResponse('项目创建成功', parsedResponse.data)
    } catch (error) {
      console.error('[TodoTool] [createProject] 创建项目异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建项目失败', error)
    }
  },

  /**
   * 更新项目
   * @param {string} projectId - 项目 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateProject(projectId, updateData) {
    console.log(`[TodoTool] [updateProject] 开始更新项目，ID: ${projectId}`, {
      updateData,
    })

    // 参数校验
    const validation = validateParams({ projectId }, ['projectId'])
    if (validation) {
      console.warn('[TodoTool] [updateProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 首先获取现有项目数据
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [updateProject] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data

      // 查找现有项目
      const existingProject = projects.find((p) => p.id === projectId)
      if (!existingProject) {
        console.error(`[TodoTool] [updateProject] 未找到项目：${projectId}`)
        return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到项目：${projectId}`)
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeUpdateData = {
        id: projectId,
        name: updateData.name !== undefined ? updateData.name : existingProject.name,
        color: updateData.color !== undefined ? updateData.color : existingProject.color,
        kind: updateData.kind !== undefined ? updateData.kind : existingProject.kind,
        closed: updateData.closed !== undefined ? updateData.closed : existingProject.closed,
      }

      // 移除空值字段
      const cleanUpdateData = removeEmptyFields(safeUpdateData)
      console.log('[TodoTool] [updateProject] 准备发送的更新数据：', cleanUpdateData)

      // 发送更新请求
      const response = await this._request('POST', `${API_CONFIG.PROJECT_URL}/${projectId}`, cleanUpdateData)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [updateProject] 更新项目失败：', parsedResponse)
        return parsedResponse
      }

      // 清除缓存
      this.cache.delete('batchData')

      console.log('[TodoTool] [updateProject] 项目更新成功')
      return createSuccessResponse('项目更新成功', parsedResponse.data)
    } catch (error) {
      console.error('[TodoTool] [updateProject] 更新项目异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新项目失败', error)
    }
  },

  /**
   * 删除项目
   * @param {string} projectId - 项目 ID
   * @returns {object} 删除结果
   */
  async deleteProject(projectId) {
    console.log(`[TodoTool] [deleteProject] 开始删除项目，ID: ${projectId}`)

    // 参数校验
    const validation = validateParams({ projectId }, ['projectId'])
    if (validation) {
      console.warn('[TodoTool] [deleteProject] 参数校验失败：', validation)
      return validation
    }

    try {
      // 发送删除请求
      const response = await this._request('DELETE', `${API_CONFIG.PROJECT_URL}/${projectId}`)
      const parsedResponse = parseHttpResponse(response)

      if (parsedResponse.errCode) {
        console.error('[TodoTool] [deleteProject] 删除项目失败：', parsedResponse)
        return parsedResponse
      }

      // 清除缓存
      this.cache.delete('batchData')

      console.log('[TodoTool] [deleteProject] 项目删除成功')
      return createSuccessResponse('项目删除成功', parsedResponse.data)
    } catch (error) {
      console.error('[TodoTool] [deleteProject] 删除项目异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除项目失败', error)
    }
  },

  /**
   * 关闭项目
   * @param {string} projectId - 项目 ID
   * @returns {object} 关闭结果
   */
  async closeProject(projectId) {
    console.log(`[TodoTool] [closeProject] 开始关闭项目，ID: ${projectId}`)

    return await this.updateProject(projectId, { closed: true })
  },

  /**
   * 重新打开项目
   * @param {string} projectId - 项目 ID
   * @returns {object} 重新打开结果
   */
  async reopenProject(projectId) {
    console.log(`[TodoTool] [reopenProject] 开始重新打开项目，ID: ${projectId}`)

    return await this.updateProject(projectId, { closed: false })
  },

  /**
   * 获取单个项目详情
   * @param {string} projectId - 项目 ID
   * @returns {object} 项目详情
   */
  async getProject(projectId) {
    console.log(`[TodoTool] [getProject] 开始获取项目详情，ID: ${projectId}`)

    // 参数校验
    const validation = validateParams({ projectId }, ['projectId'])
    if (validation) {
      console.warn('[TodoTool] [getProject] 参数校验失败：', validation)
      return validation
    }

    try {
      const batchResult = await this.getBatchData()
      if (batchResult.errCode) {
        console.error('[TodoTool] [getProject] 获取基础数据失败：', batchResult)
        return batchResult
      }

      const { projects } = batchResult.data
      const project = projects.find((p) => p.id === projectId)

      if (!project) {
        console.warn(`[TodoTool] [getProject] 未找到项目：${projectId}`)
        return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, '项目不存在')
      }

      // 简化项目数据
      const simplifiedProject = simplifyProjectData(project)

      console.log('[TodoTool] [getProject] 获取项目详情成功')
      return createSuccessResponse('获取项目详情成功', simplifiedProject)
    } catch (error) {
      console.error('[TodoTool] [getProject] 获取项目详情异常：', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取项目详情失败', error)
    }
  },
}

module.exports = {
  taskMethods,
  projectMethods,
}
